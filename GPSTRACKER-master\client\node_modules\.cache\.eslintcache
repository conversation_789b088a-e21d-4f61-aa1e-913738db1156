[{"C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\components\\LandingPage.js": "3", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\components\\LoginPage.js": "4", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\components\\SignupPage.js": "5", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\components\\ErrorBoundary.js": "6", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\components\\WelcomePage.js": "7", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\components\\GoogleSignIn.js": "8", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\hooks\\usePreventNavigation.js": "9", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\services\\api.js": "10", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\services\\gpsApi.js": "11", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\App.js": "12", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\store\\index.js": "13", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\store\\slices\\deviceSlice.js": "14", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\store\\slices\\mapSlice.js": "15", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\store\\slices\\authSlice.js": "16", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\store\\slices\\gpsSlice.js": "17", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\store\\slices\\notificationSlice.js": "18", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\store\\slices\\analyticsSlice.js": "19", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\store\\slices\\userSlice.js": "20", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\store\\slices\\geofenceSlice.js": "21", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\store\\slices\\routeSlice.js": "22", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\store\\slices\\uiSlice.js": "23", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\config\\index.js": "24", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\components\\common\\LoadingSpinner.js": "25", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\components\\tracking\\UniversalGPSTracker.js": "26", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\components\\EnhancedGPSTracker.js": "27", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\components\\ModernLandingPage.js": "28", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\contexts\\ThemeContext.js": "29", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\contexts\\LanguageContext.js": "30", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\components\\ui\\ThemeCustomizer.js": "31"}, {"size": 500, "mtime": 1752026790690, "results": "32", "hashOfConfig": "33"}, {"size": 362, "mtime": 1752026790755, "results": "34", "hashOfConfig": "33"}, {"size": 18724, "mtime": 1752031505473, "results": "35", "hashOfConfig": "33"}, {"size": 28801, "mtime": 1752046343683, "results": "36", "hashOfConfig": "33"}, {"size": 16029, "mtime": 1752049149139, "results": "37", "hashOfConfig": "33"}, {"size": 2510, "mtime": 1752026790972, "results": "38", "hashOfConfig": "33"}, {"size": 331759, "mtime": 1752063669406, "results": "39", "hashOfConfig": "33"}, {"size": 15573, "mtime": 1752049061954, "results": "40", "hashOfConfig": "33"}, {"size": 1016, "mtime": 1752026791452, "results": "41", "hashOfConfig": "33"}, {"size": 9692, "mtime": 1752026791497, "results": "42", "hashOfConfig": "33"}, {"size": 6204, "mtime": 1752026791529, "results": "43", "hashOfConfig": "33"}, {"size": 1745, "mtime": 1752031727371, "results": "44", "hashOfConfig": "33"}, {"size": 2535, "mtime": 1752029388505, "results": "45", "hashOfConfig": "33"}, {"size": 1578, "mtime": 1752029792583, "results": "46", "hashOfConfig": "33"}, {"size": 4209, "mtime": 1752029564553, "results": "47", "hashOfConfig": "33"}, {"size": 6856, "mtime": 1752029449453, "results": "48", "hashOfConfig": "33"}, {"size": 10333, "mtime": 1752029498537, "results": "49", "hashOfConfig": "33"}, {"size": 918, "mtime": 1752029815429, "results": "50", "hashOfConfig": "33"}, {"size": 631, "mtime": 1752029826763, "results": "51", "hashOfConfig": "33"}, {"size": 647, "mtime": 1752029804588, "results": "52", "hashOfConfig": "33"}, {"size": 659, "mtime": 1752029839219, "results": "53", "hashOfConfig": "33"}, {"size": 687, "mtime": 1752029848706, "results": "54", "hashOfConfig": "33"}, {"size": 9236, "mtime": 1752029542425, "results": "55", "hashOfConfig": "33"}, {"size": 10497, "mtime": 1752030973607, "results": "56", "hashOfConfig": "33"}, {"size": 863, "mtime": 1752029902705, "results": "57", "hashOfConfig": "33"}, {"size": 14487, "mtime": 1752029956638, "results": "58", "hashOfConfig": "33"}, {"size": 19604, "mtime": 1752026790926, "results": "59", "hashOfConfig": "33"}, {"size": 12189, "mtime": 1752045420314, "results": "60", "hashOfConfig": "33"}, {"size": 6223, "mtime": 1752031011394, "results": "61", "hashOfConfig": "33"}, {"size": 10941, "mtime": 1752031066162, "results": "62", "hashOfConfig": "33"}, {"size": 10311, "mtime": 1752031413055, "results": "63", "hashOfConfig": "33"}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "nrlx0m", {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 41, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\components\\LandingPage.js", ["157"], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\components\\LoginPage.js", ["158", "159"], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\components\\SignupPage.js", ["160"], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\components\\ErrorBoundary.js", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\components\\WelcomePage.js", ["161", "162", "163", "164", "165", "166", "167", "168", "169", "170", "171", "172", "173", "174", "175", "176", "177", "178", "179", "180", "181", "182", "183", "184", "185", "186", "187", "188", "189", "190", "191", "192", "193", "194", "195", "196", "197", "198", "199", "200", "201"], ["202", "203"], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\components\\GoogleSignIn.js", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\hooks\\usePreventNavigation.js", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\services\\gpsApi.js", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\App.js", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\store\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\store\\slices\\deviceSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\store\\slices\\mapSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\store\\slices\\authSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\store\\slices\\gpsSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\store\\slices\\notificationSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\store\\slices\\analyticsSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\store\\slices\\userSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\store\\slices\\geofenceSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\store\\slices\\routeSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\store\\slices\\uiSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\config\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\components\\common\\LoadingSpinner.js", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\components\\tracking\\UniversalGPSTracker.js", ["204", "205", "206", "207", "208", "209", "210"], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\components\\EnhancedGPSTracker.js", ["211"], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\components\\ModernLandingPage.js", ["212", "213"], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\contexts\\ThemeContext.js", ["214", "215"], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\contexts\\LanguageContext.js", ["216"], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\components\\ui\\ThemeCustomizer.js", ["217", "218", "219", "220"], [], {"ruleId": null, "fatal": true, "severity": 2, "message": "221", "line": 281, "column": 10, "nodeType": null}, {"ruleId": "222", "severity": 1, "message": "223", "line": 33, "column": 10, "nodeType": "224", "messageId": "225", "endLine": 33, "endColumn": 23}, {"ruleId": "222", "severity": 1, "message": "226", "line": 148, "column": 9, "nodeType": "224", "messageId": "225", "endLine": 148, "endColumn": 35}, {"ruleId": "222", "severity": 1, "message": "223", "line": 77, "column": 10, "nodeType": "224", "messageId": "225", "endLine": 77, "endColumn": 23}, {"ruleId": "222", "severity": 1, "message": "227", "line": 9, "column": 8, "nodeType": "224", "messageId": "225", "endLine": 9, "endColumn": 26}, {"ruleId": "222", "severity": 1, "message": "228", "line": 13, "column": 8, "nodeType": "224", "messageId": "225", "endLine": 13, "endColumn": 11}, {"ruleId": "222", "severity": 1, "message": "229", "line": 16, "column": 10, "nodeType": "224", "messageId": "225", "endLine": 16, "endColumn": 25}, {"ruleId": "222", "severity": 1, "message": "230", "line": 17, "column": 10, "nodeType": "224", "messageId": "225", "endLine": 17, "endColumn": 22}, {"ruleId": "222", "severity": 1, "message": "231", "line": 37, "column": 9, "nodeType": "224", "messageId": "225", "endLine": 37, "endColumn": 17}, {"ruleId": "222", "severity": 1, "message": "232", "line": 38, "column": 9, "nodeType": "224", "messageId": "225", "endLine": 38, "endColumn": 17}, {"ruleId": "222", "severity": 1, "message": "233", "line": 41, "column": 11, "nodeType": "224", "messageId": "225", "endLine": 41, "endColumn": 15}, {"ruleId": "222", "severity": 1, "message": "234", "line": 41, "column": 17, "nodeType": "224", "messageId": "225", "endLine": 41, "endColumn": 32}, {"ruleId": "222", "severity": 1, "message": "235", "line": 42, "column": 11, "nodeType": "224", "messageId": "225", "endLine": 42, "endColumn": 16}, {"ruleId": "222", "severity": 1, "message": "236", "line": 42, "column": 18, "nodeType": "224", "messageId": "225", "endLine": 42, "endColumn": 31}, {"ruleId": "222", "severity": 1, "message": "237", "line": 43, "column": 11, "nodeType": "224", "messageId": "225", "endLine": 43, "endColumn": 21}, {"ruleId": "222", "severity": 1, "message": "238", "line": 43, "column": 40, "nodeType": "224", "messageId": "225", "endLine": 43, "endColumn": 60}, {"ruleId": "222", "severity": 1, "message": "239", "line": 49, "column": 10, "nodeType": "224", "messageId": "225", "endLine": 49, "endColumn": 27}, {"ruleId": "222", "severity": 1, "message": "240", "line": 60, "column": 10, "nodeType": "224", "messageId": "225", "endLine": 60, "endColumn": 15}, {"ruleId": "222", "severity": 1, "message": "241", "line": 61, "column": 10, "nodeType": "224", "messageId": "225", "endLine": 61, "endColumn": 17}, {"ruleId": "222", "severity": 1, "message": "242", "line": 61, "column": 19, "nodeType": "224", "messageId": "225", "endLine": 61, "endColumn": 29}, {"ruleId": "222", "severity": 1, "message": "243", "line": 82, "column": 10, "nodeType": "224", "messageId": "225", "endLine": 82, "endColumn": 25}, {"ruleId": "222", "severity": 1, "message": "244", "line": 82, "column": 27, "nodeType": "224", "messageId": "225", "endLine": 82, "endColumn": 45}, {"ruleId": "222", "severity": 1, "message": "245", "line": 98, "column": 10, "nodeType": "224", "messageId": "225", "endLine": 98, "endColumn": 23}, {"ruleId": "222", "severity": 1, "message": "246", "line": 98, "column": 25, "nodeType": "224", "messageId": "225", "endLine": 98, "endColumn": 41}, {"ruleId": "222", "severity": 1, "message": "247", "line": 166, "column": 10, "nodeType": "224", "messageId": "225", "endLine": 166, "endColumn": 29}, {"ruleId": "222", "severity": 1, "message": "248", "line": 166, "column": 31, "nodeType": "224", "messageId": "225", "endLine": 166, "endColumn": 53}, {"ruleId": "222", "severity": 1, "message": "249", "line": 172, "column": 10, "nodeType": "224", "messageId": "225", "endLine": 172, "endColumn": 24}, {"ruleId": "222", "severity": 1, "message": "250", "line": 172, "column": 26, "nodeType": "224", "messageId": "225", "endLine": 172, "endColumn": 43}, {"ruleId": "222", "severity": 1, "message": "251", "line": 179, "column": 10, "nodeType": "224", "messageId": "225", "endLine": 179, "endColumn": 27}, {"ruleId": "222", "severity": 1, "message": "252", "line": 179, "column": 29, "nodeType": "224", "messageId": "225", "endLine": 179, "endColumn": 49}, {"ruleId": "222", "severity": 1, "message": "253", "line": 249, "column": 9, "nodeType": "224", "messageId": "225", "endLine": 249, "endColumn": 23}, {"ruleId": "222", "severity": 1, "message": "254", "line": 270, "column": 9, "nodeType": "224", "messageId": "225", "endLine": 270, "endColumn": 26}, {"ruleId": "222", "severity": 1, "message": "255", "line": 1057, "column": 13, "nodeType": "224", "messageId": "225", "endLine": 1057, "endColumn": 20}, {"ruleId": "256", "severity": 1, "message": "257", "line": 1131, "column": 6, "nodeType": "258", "endLine": 1131, "endColumn": 8, "suggestions": "259"}, {"ruleId": "222", "severity": 1, "message": "260", "line": 1134, "column": 9, "nodeType": "224", "messageId": "225", "endLine": 1134, "endColumn": 29}, {"ruleId": "222", "severity": 1, "message": "261", "line": 1648, "column": 9, "nodeType": "224", "messageId": "225", "endLine": 1648, "endColumn": 28}, {"ruleId": "256", "severity": 1, "message": "262", "line": 1712, "column": 6, "nodeType": "258", "endLine": 1712, "endColumn": 26, "suggestions": "263"}, {"ruleId": "222", "severity": 1, "message": "264", "line": 2081, "column": 9, "nodeType": "224", "messageId": "225", "endLine": 2081, "endColumn": 29}, {"ruleId": "222", "severity": 1, "message": "265", "line": 2085, "column": 9, "nodeType": "224", "messageId": "225", "endLine": 2085, "endColumn": 27}, {"ruleId": "222", "severity": 1, "message": "266", "line": 2089, "column": 9, "nodeType": "224", "messageId": "225", "endLine": 2089, "endColumn": 28}, {"ruleId": "222", "severity": 1, "message": "267", "line": 2093, "column": 9, "nodeType": "224", "messageId": "225", "endLine": 2093, "endColumn": 31}, {"ruleId": "222", "severity": 1, "message": "268", "line": 2108, "column": 9, "nodeType": "224", "messageId": "225", "endLine": 2108, "endColumn": 26}, {"ruleId": "256", "severity": 1, "message": "269", "line": 3734, "column": 6, "nodeType": "258", "endLine": 3734, "endColumn": 136, "suggestions": "270"}, {"ruleId": "256", "severity": 1, "message": "271", "line": 4012, "column": 6, "nodeType": "258", "endLine": 4012, "endColumn": 8, "suggestions": "272"}, {"ruleId": "222", "severity": 1, "message": "273", "line": 5418, "column": 9, "nodeType": "224", "messageId": "225", "endLine": 5418, "endColumn": 41}, {"ruleId": "222", "severity": 1, "message": "274", "line": 2171, "column": 9, "nodeType": "224", "messageId": "225", "endLine": 2171, "endColumn": 28, "suppressions": "275"}, {"ruleId": "222", "severity": 1, "message": "276", "line": 2189, "column": 9, "nodeType": "224", "messageId": "225", "endLine": 2189, "endColumn": 29, "suppressions": "277"}, {"ruleId": "222", "severity": 1, "message": "278", "line": 4, "column": 10, "nodeType": "224", "messageId": "225", "endLine": 4, "endColumn": 23}, {"ruleId": "222", "severity": 1, "message": "279", "line": 4, "column": 25, "nodeType": "224", "messageId": "225", "endLine": 4, "endColumn": 37}, {"ruleId": "222", "severity": 1, "message": "280", "line": 6, "column": 10, "nodeType": "224", "messageId": "225", "endLine": 6, "endColumn": 20}, {"ruleId": "222", "severity": 1, "message": "237", "line": 17, "column": 11, "nodeType": "224", "messageId": "225", "endLine": 17, "endColumn": 21}, {"ruleId": "222", "severity": 1, "message": "281", "line": 17, "column": 23, "nodeType": "224", "messageId": "225", "endLine": 17, "endColumn": 38}, {"ruleId": "222", "severity": 1, "message": "282", "line": 17, "column": 40, "nodeType": "224", "messageId": "225", "endLine": 17, "endColumn": 55}, {"ruleId": "256", "severity": 1, "message": "283", "line": 211, "column": 6, "nodeType": "258", "endLine": 211, "endColumn": 115, "suggestions": "284"}, {"ruleId": "256", "severity": 1, "message": "285", "line": 256, "column": 6, "nodeType": "258", "endLine": 256, "endColumn": 158, "suggestions": "286"}, {"ruleId": "222", "severity": 1, "message": "287", "line": 8, "column": 10, "nodeType": "224", "messageId": "225", "endLine": 8, "endColumn": 23}, {"ruleId": "222", "severity": 1, "message": "288", "line": 8, "column": 25, "nodeType": "224", "messageId": "225", "endLine": 8, "endColumn": 41}, {"ruleId": "256", "severity": 1, "message": "289", "line": 149, "column": 6, "nodeType": "258", "endLine": 149, "endColumn": 71, "suggestions": "290"}, {"ruleId": "256", "severity": 1, "message": "291", "line": 175, "column": 6, "nodeType": "258", "endLine": 175, "endColumn": 8, "suggestions": "292"}, {"ruleId": "222", "severity": 1, "message": "293", "line": 307, "column": 11, "nodeType": "224", "messageId": "225", "endLine": 307, "endColumn": 21}, {"ruleId": "222", "severity": 1, "message": "294", "line": 2, "column": 47, "nodeType": "224", "messageId": "225", "endLine": 2, "endColumn": 52}, {"ruleId": "222", "severity": 1, "message": "295", "line": 2, "column": 67, "nodeType": "224", "messageId": "225", "endLine": 2, "endColumn": 76}, {"ruleId": "222", "severity": 1, "message": "296", "line": 32, "column": 10, "nodeType": "224", "messageId": "225", "endLine": 32, "endColumn": 21}, {"ruleId": "222", "severity": 1, "message": "297", "line": 32, "column": 23, "nodeType": "224", "messageId": "225", "endLine": 32, "endColumn": 37}, "Parsing error: Expected corresponding JSX closing tag for <Container>. (281:10)", "no-unused-vars", "'googleLoading' is assigned a value but never used.", "Identifier", "unusedVar", "'handleForgotPasswordChange' is assigned a value but never used.", "'EnhancedGPSTracker' is defined but never used.", "'api' is defined but never used.", "'addNotification' is defined but never used.", "'setActiveTab' is defined but never used.", "'location' is assigned a value but never used.", "'dispatch' is assigned a value but never used.", "'user' is assigned a value but never used.", "'isAuthenticated' is assigned a value but never used.", "'theme' is assigned a value but never used.", "'notifications' is assigned a value but never used.", "'isTracking' is assigned a value but never used.", "'reduxDeviceLocations' is assigned a value but never used.", "'activeSidebarItem' is assigned a value but never used.", "'users' is assigned a value but never used.", "'devices' is assigned a value but never used.", "'setDevices' is assigned a value but never used.", "'expandedSection' is assigned a value but never used.", "'setExpandedSection' is assigned a value but never used.", "'showHelpModal' is assigned a value but never used.", "'setShowHelpModal' is assigned a value but never used.", "'showEnhancedTracker' is assigned a value but never used.", "'setShowEnhancedTracker' is assigned a value but never used.", "'gpsTrackerMode' is assigned a value but never used.", "'setGpsTrackerMode' is assigned a value but never used.", "'selectedQRForEdit' is assigned a value but never used.", "'setSelectedQRForEdit' is assigned a value but never used.", "'validateDevice' is assigned a value but never used.", "'generateQRPattern' is assigned a value but never used.", "'watchId' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useCallback has missing dependencies: 'showGeoapifyTracker' and 'showRealTimeMap'. Either include them or remove the dependency array.", "ArrayExpression", ["298"], "'stopRealTimeTracking' is assigned a value but never used.", "'startDeviceTracking' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'updateDeviceWithGPSLocation'. Either include it or remove the dependency array.", ["299"], "'handleDashboardClick' is assigned a value but never used.", "'handleProfileClick' is assigned a value but never used.", "'handleSettingsClick' is assigned a value but never used.", "'handleSidebarItemClick' is assigned a value but never used.", "'getDashboardStats' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'startRealTimeTracking'. Either include it or remove the dependency array.", ["300"], "React Hook useCallback has missing dependencies: 'handleEmailSupport' and 'handleShowContactModal'. Either include them or remove the dependency array.", ["301"], "'renderGPSTrackingContent_REMOVED' is assigned a value but never used.", "'getRecentActivities' is assigned a value but never used.", ["302"], "'getRoleBasedFeatures' is assigned a value but never used.", ["303"], "'startTracking' is defined but never used.", "'stopTracking' is defined but never used.", "'APP_CONFIG' is defined but never used.", "'currentLocation' is assigned a value but never used.", "'deviceLocations' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'handleStopTracking'. Either include it or remove the dependency array.", ["304"], "React Hook useCallback has a missing dependency: 'stopTracking'. Either include it or remove the dependency array.", ["305"], "'activeSection' is assigned a value but never used.", "'setActiveSection' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'applyTheme'. Either include it or remove the dependency array.", ["306"], "React Hook useEffect has a missing dependency: 'toggleAccessibility'. Either include it or remove the dependency array.", ["307"], "'dateFormat' is assigned a value but never used.", "'Badge' is defined but never used.", "'Accordion' is defined but never used.", "'customColor' is assigned a value but never used.", "'setCustomColor' is assigned a value but never used.", {"desc": "308", "fix": "309"}, {"desc": "310", "fix": "311"}, {"desc": "312", "fix": "313"}, {"desc": "314", "fix": "315"}, {"kind": "316", "justification": "317"}, {"kind": "316", "justification": "317"}, {"desc": "318", "fix": "319"}, {"desc": "320", "fix": "321"}, {"desc": "322", "fix": "323"}, {"desc": "324", "fix": "325"}, "Update the dependencies array to be: [showGeoapifyTracker, showRealTimeMap]", {"range": "326", "text": "327"}, "Update the dependencies array to be: [getCurrentLocation, updateDeviceWithGPSLocation]", {"range": "328", "text": "329"}, "Update the dependencies array to be: [userData, generatedQRCodes, canViewQRCode, getDisplayCode, canScanQRCode, viewQRCode, startEnhancedGPSTracking, handleQRCodeScan, startRealTimeTracking]", {"range": "330", "text": "331"}, "Update the dependencies array to be: [handleEmailSupport, handleShowContactModal]", {"range": "332", "text": "333"}, "directive", "", "Update the dependencies array to be: [dispatch, deviceName, deviceId, mapInstance, onLocationUpdate, calculateDistance, pathPoints, initializeMap, handleStopTracking]", {"range": "334", "text": "335"}, "Update the dependencies array to be: [trackingStartTime, mapInstance, currentMarker, onLocationUpdate, calculateDistance, maxSpeed, totalDistance, pathPolyline, locationPath, initializeMap, stopTracking]", {"range": "336", "text": "337"}, "Update the dependencies array to be: [currentTheme, customColors, accessibility, fontSize, animations, applyTheme]", {"range": "338", "text": "339"}, "Update the dependencies array to be: [toggleAccessibility]", {"range": "340", "text": "341"}, [42007, 42009], "[showGeoapifyTracker, showRealTimeMap]", [63328, 63348], "[getCurrentLocation, updateDeviceWithGPSLocation]", [137105, 137235], "[userData, generatedQRCodes, canViewQRCode, getDisplayCode, canScanQRCode, viewQRCode, startEnhancedGPSTracking, handleQRCodeScan, startRealTimeTracking]", [149909, 149911], "[handleEmailSupport, handleShowContactModal]", [7080, 7189], "[dispatch, deviceName, deviceId, mapInstance, onLocationUpdate, calculateDistance, pathPoints, initializeMap, handleStopTracking]", [9032, 9184], "[trackingStartTime, mapInstance, currentMarker, onLocationUpdate, calculateDistance, maxSpeed, totalDistance, pathPolyline, locationPath, initializeMap, stopTracking]", [4670, 4735], "[currentTheme, customColors, accessibility, fontSize, animations, applyTheme]", [5572, 5574], "[toggleAccessibility]"]