[{"C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\components\\LandingPage.js": "3", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\components\\LoginPage.js": "4", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\components\\SignupPage.js": "5", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\components\\ErrorBoundary.js": "6", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\components\\WelcomePage.js": "7", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\components\\GoogleSignIn.js": "8", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\hooks\\usePreventNavigation.js": "9", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\services\\api.js": "10", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\services\\gpsApi.js": "11", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\App.js": "12", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\store\\index.js": "13", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\store\\slices\\deviceSlice.js": "14", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\store\\slices\\mapSlice.js": "15", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\store\\slices\\authSlice.js": "16", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\store\\slices\\gpsSlice.js": "17", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\store\\slices\\notificationSlice.js": "18", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\store\\slices\\analyticsSlice.js": "19", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\store\\slices\\userSlice.js": "20", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\store\\slices\\geofenceSlice.js": "21", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\store\\slices\\routeSlice.js": "22", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\store\\slices\\uiSlice.js": "23", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\config\\index.js": "24", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\components\\common\\LoadingSpinner.js": "25", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\components\\tracking\\UniversalGPSTracker.js": "26", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\components\\EnhancedGPSTracker.js": "27", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\components\\ModernLandingPage.js": "28", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\contexts\\ThemeContext.js": "29", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\contexts\\LanguageContext.js": "30", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\components\\ui\\ThemeCustomizer.js": "31"}, {"size": 500, "mtime": 1752026790690, "results": "32", "hashOfConfig": "33"}, {"size": 362, "mtime": 1752026790755, "results": "34", "hashOfConfig": "33"}, {"size": 18724, "mtime": 1752031505473, "results": "35", "hashOfConfig": "33"}, {"size": 28801, "mtime": 1752046343683, "results": "36", "hashOfConfig": "33"}, {"size": 16029, "mtime": 1752049149139, "results": "37", "hashOfConfig": "33"}, {"size": 2510, "mtime": 1752026790972, "results": "38", "hashOfConfig": "33"}, {"size": 331283, "mtime": 1752062673439, "results": "39", "hashOfConfig": "33"}, {"size": 15573, "mtime": 1752049061954, "results": "40", "hashOfConfig": "33"}, {"size": 1016, "mtime": 1752026791452, "results": "41", "hashOfConfig": "33"}, {"size": 9692, "mtime": 1752026791497, "results": "42", "hashOfConfig": "33"}, {"size": 6204, "mtime": 1752026791529, "results": "43", "hashOfConfig": "33"}, {"size": 1745, "mtime": 1752031727371, "results": "44", "hashOfConfig": "33"}, {"size": 2535, "mtime": 1752029388505, "results": "45", "hashOfConfig": "33"}, {"size": 1578, "mtime": 1752029792583, "results": "46", "hashOfConfig": "33"}, {"size": 4209, "mtime": 1752029564553, "results": "47", "hashOfConfig": "33"}, {"size": 6856, "mtime": 1752029449453, "results": "48", "hashOfConfig": "33"}, {"size": 10333, "mtime": 1752029498537, "results": "49", "hashOfConfig": "33"}, {"size": 918, "mtime": 1752029815429, "results": "50", "hashOfConfig": "33"}, {"size": 631, "mtime": 1752029826763, "results": "51", "hashOfConfig": "33"}, {"size": 647, "mtime": 1752029804588, "results": "52", "hashOfConfig": "33"}, {"size": 659, "mtime": 1752029839219, "results": "53", "hashOfConfig": "33"}, {"size": 687, "mtime": 1752029848706, "results": "54", "hashOfConfig": "33"}, {"size": 9236, "mtime": 1752029542425, "results": "55", "hashOfConfig": "33"}, {"size": 10497, "mtime": 1752030973607, "results": "56", "hashOfConfig": "33"}, {"size": 863, "mtime": 1752029902705, "results": "57", "hashOfConfig": "33"}, {"size": 14487, "mtime": 1752029956638, "results": "58", "hashOfConfig": "33"}, {"size": 19604, "mtime": 1752026790926, "results": "59", "hashOfConfig": "33"}, {"size": 12189, "mtime": 1752045420314, "results": "60", "hashOfConfig": "33"}, {"size": 6223, "mtime": 1752031011394, "results": "61", "hashOfConfig": "33"}, {"size": 10941, "mtime": 1752031066162, "results": "62", "hashOfConfig": "33"}, {"size": 10311, "mtime": 1752031413055, "results": "63", "hashOfConfig": "33"}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "nrlx0m", {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\components\\LandingPage.js", ["157"], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\components\\LoginPage.js", ["158", "159"], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\components\\SignupPage.js", ["160"], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\components\\ErrorBoundary.js", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\components\\WelcomePage.js", ["161"], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\components\\GoogleSignIn.js", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\hooks\\usePreventNavigation.js", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\services\\gpsApi.js", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\App.js", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\store\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\store\\slices\\deviceSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\store\\slices\\mapSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\store\\slices\\authSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\store\\slices\\gpsSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\store\\slices\\notificationSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\store\\slices\\analyticsSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\store\\slices\\userSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\store\\slices\\geofenceSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\store\\slices\\routeSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\store\\slices\\uiSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\config\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\components\\common\\LoadingSpinner.js", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\components\\tracking\\UniversalGPSTracker.js", ["162", "163", "164", "165", "166", "167", "168"], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\components\\EnhancedGPSTracker.js", ["169"], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\components\\ModernLandingPage.js", ["170", "171"], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\contexts\\ThemeContext.js", ["172", "173"], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\contexts\\LanguageContext.js", ["174"], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Desktop\\ZipGps\\GPSTRACKER-master\\client\\src\\components\\ui\\ThemeCustomizer.js", ["175", "176", "177", "178"], [], {"ruleId": null, "fatal": true, "severity": 2, "message": "179", "line": 281, "column": 10, "nodeType": null}, {"ruleId": "180", "severity": 1, "message": "181", "line": 33, "column": 10, "nodeType": "182", "messageId": "183", "endLine": 33, "endColumn": 23}, {"ruleId": "180", "severity": 1, "message": "184", "line": 148, "column": 9, "nodeType": "182", "messageId": "183", "endLine": 148, "endColumn": 35}, {"ruleId": "180", "severity": 1, "message": "181", "line": 77, "column": 10, "nodeType": "182", "messageId": "183", "endLine": 77, "endColumn": 23}, {"ruleId": null, "fatal": true, "severity": 2, "message": "185", "line": 3615, "column": 28, "nodeType": null}, {"ruleId": "180", "severity": 1, "message": "186", "line": 4, "column": 10, "nodeType": "182", "messageId": "183", "endLine": 4, "endColumn": 23}, {"ruleId": "180", "severity": 1, "message": "187", "line": 4, "column": 25, "nodeType": "182", "messageId": "183", "endLine": 4, "endColumn": 37}, {"ruleId": "180", "severity": 1, "message": "188", "line": 6, "column": 10, "nodeType": "182", "messageId": "183", "endLine": 6, "endColumn": 20}, {"ruleId": "180", "severity": 1, "message": "189", "line": 17, "column": 11, "nodeType": "182", "messageId": "183", "endLine": 17, "endColumn": 21}, {"ruleId": "180", "severity": 1, "message": "190", "line": 17, "column": 23, "nodeType": "182", "messageId": "183", "endLine": 17, "endColumn": 38}, {"ruleId": "180", "severity": 1, "message": "191", "line": 17, "column": 40, "nodeType": "182", "messageId": "183", "endLine": 17, "endColumn": 55}, {"ruleId": "192", "severity": 1, "message": "193", "line": 211, "column": 6, "nodeType": "194", "endLine": 211, "endColumn": 115, "suggestions": "195"}, {"ruleId": "192", "severity": 1, "message": "196", "line": 256, "column": 6, "nodeType": "194", "endLine": 256, "endColumn": 158, "suggestions": "197"}, {"ruleId": "180", "severity": 1, "message": "198", "line": 8, "column": 10, "nodeType": "182", "messageId": "183", "endLine": 8, "endColumn": 23}, {"ruleId": "180", "severity": 1, "message": "199", "line": 8, "column": 25, "nodeType": "182", "messageId": "183", "endLine": 8, "endColumn": 41}, {"ruleId": "192", "severity": 1, "message": "200", "line": 149, "column": 6, "nodeType": "194", "endLine": 149, "endColumn": 71, "suggestions": "201"}, {"ruleId": "192", "severity": 1, "message": "202", "line": 175, "column": 6, "nodeType": "194", "endLine": 175, "endColumn": 8, "suggestions": "203"}, {"ruleId": "180", "severity": 1, "message": "204", "line": 307, "column": 11, "nodeType": "182", "messageId": "183", "endLine": 307, "endColumn": 21}, {"ruleId": "180", "severity": 1, "message": "205", "line": 2, "column": 47, "nodeType": "182", "messageId": "183", "endLine": 2, "endColumn": 52}, {"ruleId": "180", "severity": 1, "message": "206", "line": 2, "column": 67, "nodeType": "182", "messageId": "183", "endLine": 2, "endColumn": 76}, {"ruleId": "180", "severity": 1, "message": "207", "line": 32, "column": 10, "nodeType": "182", "messageId": "183", "endLine": 32, "endColumn": 21}, {"ruleId": "180", "severity": 1, "message": "208", "line": 32, "column": 23, "nodeType": "182", "messageId": "183", "endLine": 32, "endColumn": 37}, "Parsing error: Expected corresponding JSX closing tag for <Container>. (281:10)", "no-unused-vars", "'googleLoading' is assigned a value but never used.", "Identifier", "unusedVar", "'handleForgotPasswordChange' is assigned a value but never used.", "Parsing error: Expected corresponding JSX closing tag for <div>. (3615:28)", "'startTracking' is defined but never used.", "'stopTracking' is defined but never used.", "'APP_CONFIG' is defined but never used.", "'isTracking' is assigned a value but never used.", "'currentLocation' is assigned a value but never used.", "'deviceLocations' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useCallback has a missing dependency: 'handleStopTracking'. Either include it or remove the dependency array.", "ArrayExpression", ["209"], "React Hook useCallback has a missing dependency: 'stopTracking'. Either include it or remove the dependency array.", ["210"], "'activeSection' is assigned a value but never used.", "'setActiveSection' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'applyTheme'. Either include it or remove the dependency array.", ["211"], "React Hook useEffect has a missing dependency: 'toggleAccessibility'. Either include it or remove the dependency array.", ["212"], "'dateFormat' is assigned a value but never used.", "'Badge' is defined but never used.", "'Accordion' is defined but never used.", "'customColor' is assigned a value but never used.", "'setCustomColor' is assigned a value but never used.", {"desc": "213", "fix": "214"}, {"desc": "215", "fix": "216"}, {"desc": "217", "fix": "218"}, {"desc": "219", "fix": "220"}, "Update the dependencies array to be: [dispatch, deviceName, deviceId, mapInstance, onLocationUpdate, calculateDistance, pathPoints, initializeMap, handleStopTracking]", {"range": "221", "text": "222"}, "Update the dependencies array to be: [trackingStartTime, mapInstance, currentMarker, onLocationUpdate, calculateDistance, maxSpeed, totalDistance, pathPolyline, locationPath, initializeMap, stopTracking]", {"range": "223", "text": "224"}, "Update the dependencies array to be: [currentTheme, customColors, accessibility, fontSize, animations, applyTheme]", {"range": "225", "text": "226"}, "Update the dependencies array to be: [toggleAccessibility]", {"range": "227", "text": "228"}, [7080, 7189], "[dispatch, deviceName, deviceId, mapInstance, onLocationUpdate, calculateDistance, pathPoints, initializeMap, handleStopTracking]", [9032, 9184], "[trackingStartTime, mapInstance, currentMarker, onLocationUpdate, calculateDistance, maxSpeed, totalDistance, pathPolyline, locationPath, initializeMap, stopTracking]", [4670, 4735], "[currentTheme, customColors, accessibility, fontSize, animations, applyTheme]", [5572, 5574], "[toggleAccessibility]"]