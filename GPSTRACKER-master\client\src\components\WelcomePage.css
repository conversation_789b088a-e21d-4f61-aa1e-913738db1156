/* Modern Welcome Page Styles - Reset and Base */
.welcome-page * {
  box-sizing: border-box;
}

.welcome-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
  overflow-x: hidden;
  margin: 0;
  padding: 0;
  position: relative;
}

/* Remove Bootstrap default margins and paddings */
.welcome-page .container-fluid {
  padding-left: 2rem !important;
  padding-right: 2rem !important;
}

.welcome-page .navbar {
  margin-bottom: 0 !important;
}

/* Adjust Bootstrap grid system for closer positioning */
.dashboard-content .row {
  margin-left:-10px !important;
  margin-right: 0px !important;
}

.dashboard-content .col,
.dashboard-content [class*="col-"] {
  padding-left: 1px !important;
  padding-right: 1px !important;
}

/* Move all cards closer to sidebar */
.dashboard-content .card {
  margin-left: -100px !important;
}

/* Custom Navbar */
.custom-navbar {
  background: linear-gradient(135deg, #4a148c 0%, #6a1b9a 100%) !important;
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 10px rgba(0,0,0,0.15) !important;
  transition: all 0.3s ease;
  position: fixed !important;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1030;
  height: 70px;
  padding: 0.5rem 0;
}

.navbar-brand-custom {
  font-weight: 700;
  font-size: 1.5rem;
  color: #fff !important;
  text-decoration: none;
}

.custom-navbar .navbar-nav .nav-link {
  color: #fff !important;
  font-weight: 600 !important;
  padding: 0.6rem 1.2rem !important;
  border-radius: 8px !important;
  transition: all 0.3s ease !important;
  margin: 0 0.3rem !important;
}

.custom-navbar .navbar-brand {
  color: #fff !important;
}

.nav-link-custom:hover,
.custom-navbar .navbar-nav .nav-link:hover {
  background-color: rgba(255,255,255,0.1) !important;
  border-color: rgba(255,255,255,0.4) !important;
  transform: translateY(-1px);
}

.navbar-brand-custom:hover {
  background-color: rgba(255,255,255,0.1) !important;
  border-color: rgba(255,255,255,0.4) !important;
  transform: translateY(-1px);
}

/* Login Button Orange Styling */
.custom-navbar .btn-outline-light,
.custom-navbar .login-btn {
  background: linear-gradient(135deg, #ff6b35, #f7931e) !important;
  border: 2px solid #ff6b35 !important;
  color: white !important;
  font-weight: 600 !important;
  border-radius: 8px !important;
  padding: 0.5rem 1.2rem !important;
  transition: all 0.3s ease !important;
}

.custom-navbar .btn-outline-light:hover,
.custom-navbar .login-btn:hover {
  background: linear-gradient(135deg, #f7931e, #ff6b35) !important;
  border-color: #f7931e !important;
  color: white !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 15px rgba(255, 107, 53, 0.4) !important;
}

.nav-link-custom {
  color: #4a148c !important;
  font-weight: 500;
  padding: 0.5rem 1rem !important;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.nav-link-custom:hover {
  background: rgba(74, 20, 140, 0.1);
  color: #4a148c !important;
  transform: translateY(-1px);
}

.logout-btn {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white !important;
  border-radius: 8px;
  margin-left: 0.5rem;
}

.logout-btn:hover {
  background: linear-gradient(135deg, #ee5a24, #ff6b6b);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(238, 90, 36, 0.3);
}

/* Main Layout */
.main-layout {
  display: flex;
  min-height: calc(100vh - 70px);
  margin-top: 70px;
  width: 100%;
  position: relative;
}

/* Sidebar */
.dashboard-sidebar {
  width: 300px;
  background: #2c3e50;
  overflow-y: auto;
  position: fixed;
  top: 70px;
  left: 0;
  height: calc(100vh - 70px);
  z-index: 1000;
  flex-shrink: 0;
  border-right: none;
  box-shadow: none;
}

.sidebar-section .accordion-header {
  background: transparent;
  border: none;
}

.sidebar-header {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  font-weight: 600;
  border: none;
  padding: 1rem 1.5rem;
  transition: all 0.3s ease;
}

.sidebar-header:hover {
  background: linear-gradient(135deg, #764ba2, #667eea);
  transform: translateX(5px);
}

.sidebar-item {
  padding: 0.75rem 2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
  color: #4a5568;
  font-weight: 500;
}

.sidebar-item:hover {
  background: rgba(102, 126, 234, 0.1);
  border-left-color: #667eea;
  transform: translateX(5px);
  color: #667eea;
}

.sidebar-item.active {
  background: linear-gradient(90deg, rgba(102, 126, 234, 0.15), transparent);
  border-left-color: #667eea;
  color: #667eea;
  font-weight: 600;
}

/* Modern Sidebar Navigation */
.sidebar-nav {
  padding: 0;
  margin: 0;
  list-style: none;
}

.sidebar-nav-item {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  color: #ecf0f1;
  text-decoration: none;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
  border-left: 3px solid transparent;
}

.sidebar-nav-item:hover {
  background: #34495e;
  color: #3498db;
  border-left-color: #3498db;
  text-decoration: none;
}

.sidebar-nav-item.active {
  background: #34495e;
  color: #3498db;
  border-left-color: #3498db;
}

/* Icon styles for navigation items */
.sidebar-nav-item .nav-icon {
  width: 20px;
  margin-right: 12px;
  font-size: 16px;
  text-align: center;
}

/* Direct Logout Button Styling */
.sidebar-item.logout-item {
  background: #e74c3c !important;
  color: white !important;
  font-weight: 600;
  transition: all 0.3s ease;
}

.sidebar-item.logout-item:hover {
  background: #c0392b !important;
  transform: translateX(5px);
}

/* Main Content */
.dashboard-content {
  flex: 1;
  margin-left:auto;
  padding: 0;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  min-height: calc(100vh - 70px);
  width: calc(100% - 300px);
  overflow-y: auto;
}

/* Cards */
.card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  transition: all 0.3s ease;
  margin-left: 10px;
  margin-right: 10px;
  min-height: 120px;
  font-size: 0.9rem;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
}

.card-header {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  border-radius: 12px 12px 0 0 !important;
  padding: 0.75rem 1rem;
  font-weight: 600;
  font-size: 0.95rem;
}

.card-body {
  padding: 1rem !important;
  font-size: 0.9rem;
}

/* Buttons */
.btn {
  border-radius: 12px;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  transition: all 0.3s ease;
  border: none;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #764ba2, #667eea);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-success {
  background: linear-gradient(135deg, #56ab2f, #a8e6cf);
  box-shadow: 0 4px 15px rgba(86, 171, 47, 0.3);
}

.btn-warning {
  background: linear-gradient(135deg, #f093fb, #f5576c);
  box-shadow: 0 4px 15px rgba(245, 87, 108, 0.3);
}

.btn-info {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
  box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
}

.btn-outline-primary {
  border: 2px solid #667eea;
  color: #667eea;
  background: transparent;
}

.btn-outline-primary:hover {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
}

/* Quick Actions */
.quick-actions-container {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
  text-align: center;
}

.action-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  transition: all 0.3s ease;
}

.action-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.action-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  border-radius: 12px;
  color: white;
  font-weight: 600;
  padding: 1rem 1.5rem;
  transition: all 0.3s ease;
  text-align: left;
}

.action-btn:hover {
  background: linear-gradient(135deg, #764ba2, #667eea);
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.action-btn-secondary {
  background: linear-gradient(135deg, #56ab2f, #a8e6cf);
}

.action-btn-warning {
  background: linear-gradient(135deg, #f093fb, #f5576c);
}

/* Tables */
.table {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.table thead th {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  font-weight: 600;
  padding: 1rem;
}

.table tbody tr {
  transition: all 0.3s ease;
}

.table tbody tr:hover {
  background: rgba(102, 126, 234, 0.05);
  transform: scale(1.01);
}

/* Badges */
.badge {
  border-radius: 8px;
  font-weight: 600;
  padding: 0.5rem 0.75rem;
}

/* Alerts */
.alert {
  border: none;
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.alert-success {
  background: linear-gradient(135deg, rgba(86, 171, 47, 0.1), rgba(168, 230, 207, 0.1));
  border-left: 4px solid #56ab2f;
}

.alert-info {
  background: linear-gradient(135deg, rgba(79, 172, 254, 0.1), rgba(0, 242, 254, 0.1));
  border-left: 4px solid #4facfe;
}

.alert-warning {
  background: linear-gradient(135deg, rgba(240, 147, 251, 0.1), rgba(245, 87, 108, 0.1));
  border-left: 4px solid #f093fb;
}

/* Progress Bars */
.progress {
  border-radius: 10px;
  height: 12px;
  background: rgba(255, 255, 255, 0.3);
}

.progress-bar {
  border-radius: 10px;
  background: linear-gradient(135deg, #667eea, #764ba2);
}

/* Forms */
.form-control {
  border: 2px solid rgba(102, 126, 234, 0.2);
  border-radius: 12px;
  padding: 0.75rem 1rem;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.9);
}

.form-control:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
  background: white;
}

/* Modals */
.modal-content {
  border: none;
  border-radius: 20px;
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
  border: none;
  border-radius: 20px 20px 0 0;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.modal-footer {
  border: none;
  border-radius: 0 0 20px 20px;
  background: rgba(248, 249, 250, 0.5);
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-sidebar {
    width: 100%;
    position: relative;
    height: auto;
    top: 0;
    left: 0;
  }

  .dashboard-content {
    margin-left: 0;
    padding: 0.75rem 1rem 1rem 0rem;
    width: 100%;
  }

  .dashboard-content .row {
    margin-left: 0px !important;
    margin-right: 10px !important;
  }

  .dashboard-content .col,
  .dashboard-content [class*="col-"] {
    padding-left: 0px !important;
    padding-right: 10px !important;
  }

  .main-layout {
    flex-direction: column;
    margin-top: 70px;
  }

  .welcome-header {
    padding: 1rem;
    margin-bottom: 1rem;
  }

  .welcome-header h1 {
    font-size: 1.5rem;
  }

  .action-card {
    margin-bottom: 1rem;
  }

  .custom-navbar {
    height: 60px;
  }

  .main-layout {
    margin-top: 60px;
  }

  .dashboard-sidebar {
    height: auto;
  }
}

/* Loading States */
.loading-spinner {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  border: 3px solid rgba(102, 126, 234, 0.3);
  border-radius: 50%;
  border-top-color: #667eea;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Welcome Header */
.welcome-header {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 15px;
  margin-left: -15px;
  margin-right: 0px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Specific adjustments for dashboard content boxes */
.dashboard-content .mb-4 {
  margin-bottom: 1rem !important;
}

.dashboard-content .shadow-sm {
  margin-left: 0 !important;
}

/* Quick Actions and Login History positioning */
.dashboard-content .g-3 {
  --bs-gutter-x: 1.5rem !important;
  --bs-gutter-y: 0.5rem !important;
}

/* Remove any container padding that creates gaps */
.dashboard-content .container,
.dashboard-content .container-fluid {
  padding-left: 0 !important;
  padding-right: 15px !important;
}

/* Ensure cards start from the very left */
.dashboard-content .mb-4:first-child {
  margin-left: 0 !important;
}

/* Remove any default Bootstrap spacing */
.dashboard-content .row:first-child {
  margin-top: 0 !important;
}

.welcome-header h1 {
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

/* Stats Cards */
.stats-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  border-radius: 16px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.stats-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

.stats-number {
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
